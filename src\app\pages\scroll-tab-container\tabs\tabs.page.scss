
input,
select {
  border: none;
  outline: none;
  box-shadow: none;
  background: transparent; /* Optional: if you want transparent background */
}
button{
  background-color: transparent;
}

// -------------
.patient-details {
  background-color: white;
  top: 30px;
  left: 10px;
  position: relative;
  border-radius: 8px;
  /* Ensure visibility in Electron */
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: 100%;
  z-index: 1;
}

// ------------------------- scroll-tab-css-----------------// Scrollable Tabs

.scroll-tabs-wrapper {
  width: 100%;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  white-space: nowrap;
  padding: 12px 0;
  background: white;
  border-radius: 8px;
  padding: 16px;
  border-bottom: 1px solid #E5E7EB;
  /* Electron-specific fixes */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  /* Ensure proper display */
  display: block !important;
  position: relative;

  /* Show scrollbar in Electron */
  &::-webkit-scrollbar {
    height: 8px !important;
    display: block !important;
    width: auto !important;
    background: transparent !important;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px;
    display: block !important;
  }

  &::-webkit-scrollbar-thumb {
    background: #ffffff !important;
    border-radius: 4px;
    display: block !important;
  }

  &::-webkit-scrollbar-thumb:hover {
    // background: #ffffff !important;
  }

  // .tab-container {
  //   min-width: max-content !important;
  //   display: inline-flex !important;
  //   flex-wrap: nowrap !important;
  //   width: auto !important;
  //   /* Electron-specific fixes */
  //   overflow-x: auto !important;
  //   overflow-y: hidden !important;
  //   white-space: nowrap !important;
  //   /* Ensure visibility */
  //   opacity: 1 !important;
  //   visibility: visible !important;
  // }

 }
.scroll-tabs-wrapper {
 width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  padding: 12px 0;
  background: white;
  border-radius: 8px;
  padding: 16px;
  border-bottom: 1px solid #E5E7EB;
 }
  .tab-header {
    min-width: max-content;
    display: inline-flex;
    flex-wrap: nowrap;
  }

  .button-native {
     flex-shrink: 0;
    white-space: nowrap;
    font-size: 16px;
    width: 158px;
    font-weight: 400;
    text-transform: capitalize;
    height: 56px;
    font-family: 'DM Sans', sans-serif;

    color: #4A4A48;
     /* Active state */
  &.active {
    color: #007bff !important;
    border-bottom: 2px solid #007bff !important;
  }

  &:hover {
    background: rgba(241, 241, 241, 0.05);
  }

  }








// ------------------ Tab Panel Content
.tab-content {
  width: 100%;
  padding-top: 0;
  background-color: white;
  /* Ensure content is visible */
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  min-height: 400px;

  /* Fix for Electron */
  overflow-y: auto !important;
  overflow-x: hidden;
  max-height: calc(100vh - 300px);

  /* Debug styling */
  .debug-info {
    padding: 5px;
    background: #f0f0f0;
    border-radius: 4px;
    font-family: monospace;
  }
}

.tab-panel {
  border-radius: 8px;
  /* Ensure panels are visible */
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: 100%;
  // padding: 16px;

  /* Animation for smooth transitions */
  transition: opacity 0.3s ease-in-out;
}
.cards{
  padding: 16px;
}

/* Ensure ng-container content is visible */
ng-container {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Electron-specific fixes for tab switching */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .tab-content {
    /* Force repaint in Electron */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  .tab-panel {
    /* Ensure proper rendering */
    will-change: opacity, transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}




// ---------------------- complaint css -----------------

.ion-inherit-color {
  font-family: "DM Sans", sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #4A4A48;
  // width: 1289px;
  // height: 37px;
  transform: rotate(0deg);
  // opacity: 1;
  gap: 10px;
  // padding: 8px 16px;
}

.complaint-form-row {
  width: 87%;
  display: flex;
  gap:62px;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 27px;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.2px;
  line-height: 104%;



  input,
  select {
    flex: 1;
  }

  .btn-add {
    font-family: 'DM Sans', sans-serif;
    font-weight: 600;
    font-style: normal;
    /* 'SemiBold' is not a valid value; use font-weight for boldness */
    font-size: 14px;
    line-height: 140%;
    letter-spacing: 0.4px;
    text-align: center;
    vertical-align: middle;
    // width: 120px;
    height: 48px;
    text-transform: capitalize;
    position: relative;
    display: flex;
    gap: 8px;
    top: 12px;
    bottom: 12px;
    left: 24px;
    right: 24px;
    margin-top: 29px;
    margin-right: 24px;
    margin-left: -17px;
  }

  .btn-add span {
    font-family: 'DM Sans', sans-serif;
    font-weight: 600;
    font-style: normal;
    font-size: 14px;
    line-height: 140%;
    letter-spacing: 0.4px;
    text-align: center;
    vertical-align: middle;
    color: #007AFF;
  }

  .btn-add img{
    width: 24px;
    height: 24px;
  }
}

.complaint-form-row label {
  top: 10px;
  position: relative;
  right: 16px;
  bottom: 12px;
  left: 16px;
  //  width: 472.5px;
  height: 42px;
  display: block;
}

.complaint-input {
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  padding-left: 20px;
  width: 472.5px;
  height: 48px;
  /* margin-top: 9px; */
  top: 10px;
  position: relative;
  right: 16px;
  bottom: 12px;
  left: 16px;
}

.complaint-input-2 {
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  padding-left: 23px;
  width: 228.25px;
  height: 48px;
  /* margin-right: 15px; */
  /* margin-top: 9px; */
  gap: 10px;
  display: flex;
}

// .complaints-table css
.complaints-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  // border-radius: 10px;
  overflow: hidden;
  // box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.complaints-table th,
.complaints-table td {
  font-family: 'DM Sans', sans-serif;
  /* Ensure the font is imported */
  font-weight: 500;
  /* 500 = Medium */
  font-style: normal;
  /* 'Medium' is not valid here; use 'normal' */
  font-size: 14px;
  line-height: 150%;
  /* Or use 1.5 */
  letter-spacing: 0;
  /* Use 0 instead of 0% */
  // vertical-align: middle;
  height: 40px;
  // padding: 20px;
}

.complaints-table th {
  text-align: left;
  background-color: #D6E9FF;
  font-size: 12px;
  font-weight: 400;
  color: #374151;
}

.complaints-table tr {
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  font-weight: 400;
  color: #374151;
}

.complaints-table tr.highlight {
  background: #D1FAE5;
}

.complaints-table tr:last-child {
  border-bottom: none;
}

.complaints-table td {
  // vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 12px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 17px;
  color: #6c757d;
  transition: color 0.2s;
  height: 24px;
  width: 24px;
}

.action-icons .icon:hover {
  color: #1e90ff;
}

.complaints-table tr:nth-child(even) {
  background-color: #F9FAFB;
}

.complaints-table th:nth-child(1),
.complaints-table td:nth-child(1) {
  width: 180px;
  /* SNOMED CT */
}

.complaints-table th:nth-child(2),
.complaints-table td:nth-child(2) {
  width: 821px;
  /* Complaint Texting */
}

.complaints-table th:nth-child(3),
.complaints-table td:nth-child(3) {
  width: 150px;
  /* Since */
}

.complaints-table th:nth-child(4),
.complaints-table td:nth-child(4) {
  width: 58px;
  /* Action */
}

@media (max-width: 600px) {

  .complaints-table th,
  .complaints-table td {
    padding: 8px 6px;
    font-size: 13px;
  }
}

//-------------------- past-records.component.scss
.past-records-card {
  .filter-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    // margin-top: 10px;
    padding: 16px;
  }
    .chip-container {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .icons-container {
      display: flex;
      gap: 16px;
      align-items: center;
      width: 271px;
      height: 48px;

      button {
        width: 125px;
        font-size: 14px;
        color: #007bff;
        display:flex;


        ion-icon {
          margin-right: 6px;
        }
      }
    }
  }

  .record-noti {
    display: flex;
    background: transparent;
    border: 1px solid #D1D5DB;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 500;
    font-family: "DM Sans", sans-serif;
    color: #4A4A48;
    padding: 8px;
  }

  .record-noti span img {
    margin-left: 20px;
  }

  .filters-text {
    color: #007AFF;
    font-size: 14px;
    font-weight: 600;
    font-family: "DM Sans", sans-serif;
    margin-left: 9px;
    letter-spacing: 0.4px;
    line-height: 140%;
  }

  // -- table css
  /* New Table Styles */
  .new-complaints-table {
    width: 100%;
    border-collapse: collapse;
    // background: #f9f9ff;
    overflow: hidden;
    // margin-top: 40px;
  }

  .new-complaints-table th,
  .new-complaints-table td {
    // padding: 10px 14px;
    text-align: left;
    font-size: 14px;
  }

  .new-complaints-table th {
    background: #D6E9FF;
    font-weight: 400;
    color: #374151;
    /* border-bottom: 1px solid #b3c6e7; */
    font-size: 12px;
    height: 33px;
  }

  .new-complaints-table tr {
    // border-bottom: 1px solid #e3e3f3;
    height: 40px;
    font-size: 14px;
    font-weight: 500;
    left: 150%;
    color: #374151;
  }

  .new-complaints-table tr:last-child {
    border-bottom: none;
  }

  .new-complaints-table td {
    vertical-align: middle;
  }

  .action-icons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-right: 103px;
  }

  .new-action-icons .icon {
    cursor: pointer;
    font-size: 16px;
    color: #FFFFFF;
    transition: color 0.2s;
  }

  .new-action-icons .icon:hover {
    color: #1976d2;
  }

  @media (max-width: 600px) {

    .new-complaints-table th,
    .new-complaints-table td {
      padding: 7px 4px;
      font-size: 12px;
    }
  }

  .new-complaints-table th:nth-child(1),
  .new-complaints-table td:nth-child(1) {
    width: 120px;
    /* SNOMED CT */
    padding-left: 4px;
  }

  .new-complaints-table th:nth-child(2),
  .new-complaints-table td:nth-child(2) {
    width: 80px;
    /* Complaint Texting */
  }

  .new-complaints-table tr:nth-child(even) {
    background-color: #F9FAFB;
  }


  .new-complaints-table th:nth-child(3),
  .new-complaints-table td:nth-child(3) {
    width: 785px;
    /* Since */
  }

  .new-complaints-table th:nth-child(4),
  .new-complaints-table td:nth-child(4) {
    width: 150px;
    /* Action */
  }

  .new-complaints-table th:nth-child(5),
  .new-complaints-table td:nth-child(5) {
    width: 150px;
    /* Action */
  }

// / ---------------- past record-css -------------------- /
//table css
/* New Table Styles */
.new-past-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  font-family: 'Inter', Arial, sans-serif;
}

.new-past-table th,
.new-past-table td {
  text-align: left;
  font-size: 14px;
}

.new-past-table th {
  background: #D6E9FF;
  font-weight: 400;
  color: #374151;
  font-size: 12px;
  height: 33px;
}

.new-past-table tr {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.new-past-table tr:last-child {
  border-bottom: none;
}

.new-past-table td {
  vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-right: 24px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  transition: color 0.2s;
}

.action-icons .icon:hover {
  color: #1976d2;
}

@media (max-width: 600px) {

  .new-past-table th,
  .new-past-table td {
    padding: 7px 4px;
    font-size: 12px;
  }
}

.new-past-table th:nth-child(1),
.new-past-table td:nth-child(1) {
  width: 120px;
  padding-left: 4px;
}

.new-past-table th:nth-child(2),
.new-past-table td:nth-child(2) {
  width: 80px;
}

.new-past-table th:nth-child(3),
.new-past-table td:nth-child(3) {
  width: 785px;
}

.new-past-table th:nth-child(4),
.new-past-table td:nth-child(4) {
  width: 150px;
}
.new-past-table th:nth-child(5),
.new-past-table td:nth-child(5) {
  width: 58px;
}

.new-past-table tr:nth-child(even) {
  background-color: #F9FAFB;
}

  //button css
  ion-row {
    background-color: #F9FAFB;
    padding: 7px 0;
    border-bottom: 1px solid #eee;
    align-items: center;

    &:last-child {
      border-bottom: none;
    }
  }

  .trash-icon {
    font-size: 18px;
    color: #999;
    cursor: pointer;

    &:hover {
      color: red;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    gap: 15px;

    select {
      width: 97px;
      background: transparent;
      border: 1px solid #D1D5DB;
      border-radius: 9px;
      font-size: 12px;
      font-weight: 500;
      font-family: "DM Sans", sans-serif;
      color: #4A4A48;
      padding: 11px 7px;
    }

    .page-btn {
      display: flex;
      align-items: center;
      width: 209px;
      height: 45px;
      background: transparent;
      border: 1px solid #D1D5DB;
      border-radius: 9px;
      font-size: 12px;
      font-weight: 500;
      font-family: "DM Sans", sans-serif;
      color: #4A4A48;
      padding: 0px 8px 0 8px;
    }

    .page-info {
      font-size: 14px;
      color: #444;
    }
  }


// ---------------- parameters.page.scss
.parameters-container {
  max-width: 1200px;
  padding: 16px;
}

.legend {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  margin-top:18px;
}

.legend-item {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  color: white;
}

.status-button {
  display: flex;
  align-items: center;
  border: 1px solid #d3d6db;
  border-radius: 9px;
  padding: 0;
  overflow: hidden;
  background-color: #f9fafb;
  font-family: 'DM Sans', sans-serif;
  font-size: 14px;
  color: #333;
  height: 32px;
  width: 123px;
}

.status-indicator {
  width: 32px;
  height: 100%;
  background-color: #d3d6db;
}

.status-label {
  padding: 0 12px;
    width: 106px;
    font-size: 11px;
    letter-spacing: 0.2px;
}


.ble-status {
  margin-left: auto;
  color: #10B981;
  font-weight: 500;
  border: 1px solid #10B981;
  padding: 8px 16px;
  width: 165px;
  font-size: 12px;
  border-radius: 72px;
  height: 36px;
}

.section-title {
  font-size: 18px;
  margin-bottom: 28px;
  margin-top: 28px;
  /* font-size: 12px; */
  font-weight: 400;
  line-height: 140%;
  color: #4B5563;
}
.section-title2 {

  padding-top: 35px;

}


.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;
}

.card {
 border: 1px solid #cac8c8;
  border-left-width: 8px;
  padding: 12px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 137px;
  justify-content: space-between;
  // width: 196px;
  box-shadow: 2px 2px 4px #c5c5c5;
}

.card-icon {
  /* font-size: 24px; */
  opacity: 0.7;
  display: flex;
  justify-content: space-between;
}

.img1 {
  width: 32px;
  height: 32px;
}

.img2 {
  width: 24px;
  height: 24px;
}

.card-title {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  // letter-spacing: 0.4px;

}

.card-value {
  font-size: 12px;
  color: #374151;
  font-weight: 400;
}

/* Card status color indicators (left border) */
.card.connected {
  border-left-color: #007AFF;
}

.card.disconnected {
  border-left-color: #F59E0B;
}

.card.completed {
  border-left-color: #10B981;
}

.card.disabled {
  border-left-color: #D1D5DB;
  opacity: 0.5;
}




// ---------------- diggonosis css -------------
.diagnosis-card {
  // padding: 16px;
}

ion-card-content {
  // margin-top: 30px;
}

.diagnosis-header {
  margin-top: 16px;
  padding: 5px;
  height: 17px;
  display: block;
  font-size: 16px;
    margin-bottom: 30px;
    font-weight: 400;
  font-family: "DM Sans", sans-serif;

}

.select-diagnosis {

  display: flex;
  width: 1052px;
  justify-content: space-between;

}

.item-inner {
  border: none !important;
}

.select-diagnosis span {

  font-size: 14px;
  width: 100%;
  margin-top: 16px;
  color: #111827;
}

.dig-input-contain {
  display: flex;
}

.dig-input-contain span {
  width: 98px;
  padding: 9px;
  margin-left: 25px;
  font-size: 17px;
  color: #007AFF;
  font-weight: 600;
}

.diagnosis-input {
  width: 1052px;
  background: transparent;
  border: 1px solid #D1D5DB;
  border-radius: 9px;
  font-size: 12px;
  font-weight: 500;
  font-family: "DM Sans", sans-serif;
  color: #4A4A48;
  padding: 0px 8px 0 8px;
}

.diagnosis-segment {
  margin-bottom: 16px;
}

.diagnosis-input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  margin-top: 39px;
}

.diagnosis-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  justify-content: space-between;
}
.diagnosis-checkbox label{
 font-size: 16px;
  font-weight: 400;
  font-family: "DM Sans", sans-serif;

}

//table css
/* New Table Styles */
.new-complaints-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  font-family: 'Inter', Arial, sans-serif;
}

.new-complaints-table th,
.new-complaints-table td {
  text-align: left;
  font-size: 14px;
}

.new-complaints-table th {
  background: #D6E9FF;
  font-weight: 400;
  color: #374151;
  font-size: 12px;
  height: 33px;
}

.new-complaints-table tr {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.new-complaints-table tr:last-child {
  border-bottom: none;
}

.new-complaints-table td {
  vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-right: 24px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  transition: color 0.2s;
}

.action-icons .icon:hover {
  color: #1976d2;
}

@media (max-width: 600px) {

  .new-complaints-table th,
  .new-complaints-table td {
    padding: 7px 4px;
    font-size: 12px;
  }
}

.new-complaints-table th:nth-child(1),
.new-complaints-table td:nth-child(1) {
  width: 100px;
  padding-left: 4px;
}

.new-complaints-table th:nth-child(2),
.new-complaints-table td:nth-child(2) {
  width: 1289px;
}

.new-complaints-table th:nth-child(3),
.new-complaints-table td:nth-child(3) {
  width: 80px;
}

.new-complaints-table th:nth-child(4),
.new-complaints-table td:nth-child(4) {
  width: 58px;
}

.new-complaints-table tr:nth-child(even) {
  background-color: #F9FAFB;
}




// --------------------------- medicin page css -----------------
.medicines-section {
  max-width: 1200px;
  margin: 0 auto;
}

.medicines-cont {
  display: flex;
  padding: 16px;
}

.medicines-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 18px;
  padding: 16px;
}

.medicines-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  margin-bottom: 8px;
}

.previous-prescription {
  display: flex;
  align-items: center;
  color: #1976d2;
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  gap: 6px;
  transition: color 0.2s;
}

.previous-prescription:hover {
  color: #1251a3;
}

.previous-prescription .icon {
  font-size: 18px;
  display: inline-block;
  transform: rotate(-90deg);
}

.medicines-form {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  background: transparent;
  width: 1037px;
  // height: 196px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 7px;
}

.form-label {
  font-size: 13px;
  color: #6c6f7a;
  font-weight: 500;
}

.form-control,
.form-select {
  border: 1px solid #d3d7df;
  border-radius: 8px;
  padding: 12px 14px;
  font-size: 15px;
  font-family: inherit;
  color: #222;
  background: #fff;
  outline: none;
  transition: border 0.2s;
}

.form-control:focus,
.form-select:focus {
  border-color: #1976d2;
}

.form-select {
  appearance: none;
  background: url('data:image/svg+xml;utf8,<svg fill="%23a0a4ae" height="18" viewBox="0 0 24 24" width="18" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>') no-repeat right 12px center/18px 18px;
}

.required {
  color: #e53935;
  margin-left: 2px;
}

.add-btn-group {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  margin-top: 8px;
}

.add-btn {
  display: flex;
  align-items: flex-end;
  width: 120px;
  gap: 8px;
  background: none;
  border: none;
  font-weight: 600;
  cursor: pointer;
  padding: 0 8px;
  transition: color 0.2s;
  margin-left: 39px;
  font-size: 17px;
  color: #007AFF;
  font-weight: 600;
}

.add-btn .icon {
  font-size: 20px;
  font-weight: 700;
}

.add-btn:hover {
  color: #1251a3;
}

@media (max-width: 900px) {
  .medicines-form {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 600px) {
  .medicines-form {
    grid-template-columns: 1fr;
  }

  .medicines-section {
    padding: 0;
  }
}




// --------------------- intervention tab
.intervention-card {
  padding: 16px;
}

.plan-container {
  width: 533px;
  min-height: inherit;
  border: 1px solid #D1D5D4;
  padding: 12px 16px;
  border-radius: 8px;
}

input.has-value+.custom-placeholder {
  display: none;
}

.intervention-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
      margin-top: 16px;
}

.intervention-group {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
}

.intervention-label {
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 13px;
  padding: 12px 016px;
  color: #333;
  letter-spacing: 0.2px;
  line-height: 140%;
}

.intervention-textarea {
  width: 408.3299865722656px;
  border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  border: none;
  font-size: 14px;
}

.intervention-followup {
  margin-top: 16px;
  max-width: 300px;
}

.intervention-date {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  font-size: 14px;
}


.native-date-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  color: #333;
  background-color: #fff;
}

// ------------------ investi tab
/* New Table Styles */
.lab-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  font-family: 'Inter', Arial, sans-serif;
}

.lab-table th,
.lab-table td {
  text-align: left;
  font-size: 14px;
}

.lab-table th {
  background: #D6E9FF;
  font-weight: 400;
  color: #374151;
  font-size: 12px;
  height: 33px;
}

.lab-table tr {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.lab-table tr:last-child {
  border-bottom: none;
}

.lab-table td {
  vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-right: 24px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  transition: color 0.2s;
}

.action-icons .icon:hover {
  color: #1976d2;
}

@media (max-width: 600px) {

  .lab-table th,
  .lab-table td {
    padding: 7px 4px;
    font-size: 12px;
  }
}

.lab-table th:nth-child(1),
.lab-table td:nth-child(1) {
  width: 535.5px;
  padding-left: 4px;
}

.lab-table th:nth-child(2),
.lab-table td:nth-child(2) {
  width: 535.5px;
}

.lab-table th:nth-child(3),
.lab-table td:nth-child(3) {
  width: 80px;
}

.lab-table th:nth-child(4),
.lab-table td:nth-child(4) {
  width: 58px;
}

.lab-table tr:nth-child(even) {
  background-color: #F9FAFB;
}

// ------------------ refferal css

/* Referral Table Styles (based on lab-table) */
.referral-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  font-family: 'Inter', Arial, sans-serif;
}

.referral-table th,
.referral-table td {
  text-align: left;
  font-size: 14px;
}

.referral-table th {
  background: #D6E9FF;
  font-weight: 400;
  color: #374151;
  font-size: 12px;
  height: 33px;
}

.referral-table tr {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.referral-table tr:last-child {
  border-bottom: none;
}

.referral-table td {
  vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-right: 24px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  transition: color 0.2s;
}

.action-icons .icon:hover {
  color: #1976d2;
}

@media (max-width: 600px) {

  .referral-table th,
  .referral-table td {
    padding: 7px 4px;
    font-size: 12px;
  }
}

.referral-table th:nth-child(1),
.referral-table td:nth-child(1) {
  width: 300px;
  padding-left: 4px;
}

.referral-table th:nth-child(2),
.referral-table td:nth-child(2) {
  width: 867px;
}

.referral-table th:nth-child(3),
.referral-table td:nth-child(3) {
  width: 58px;
}

.referral-table tr:nth-child(even) {
  background-color: #F9FAFB;
}
