 <div class="diagnosis-card cards">
                <ion-card-header>
                  <div class=" ion-inherit-color">Diagnosis</div>
                </ion-card-header>
                <div>

                  <label class="diagnosis-header">How would you like to add Diagnosis?</label>

                  <!-- Debug button -->
                  <button (click)="printDiagnosisDataToConsole()" style="padding: 5px 10px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 3px; font-size: 12px;">
                    Print Diagnosis Data to Console
                  </button>

                  <div class="select-diagnosis">
                    <span class="diagnosis-radio-item">
                      <input type="radio"
                             name="diagnosisMode"
                             value="select"
                             [checked]="diagnosisMode === 'select'"
                             (change)="onDiagnosisModeChange('select')">
                      <label>Select Diagnosis</label>
                    </span>

                    <span class="diagnosis-radio-item" style="border: none;">
                      <input type="radio"
                             name="diagnosisMode"
                             value="chapter"
                             [checked]="diagnosisMode === 'chapter'"
                             (change)="onDiagnosisModeChange('chapter')">
                      <label>Select Chapter</label>
                    </span>

                    <span class="diagnosis-radio-item">
                      <input type="radio"
                             name="diagnosisMode"
                             value="manual"
                             [checked]="diagnosisMode === 'manual'"
                             (change)="onDiagnosisModeChange('manual')">
                      <label>Enter Diagnosis Manually (Free Text)</label>
                    </span>
                  </div>
                  <div class="diagnosis-input-group">
                    <div class="diagnosis-checkbox">
                      <label>Diagnosis</label>
                      <div>
                        <input type="checkbox" [(ngModel)]="isProvisional" style="margin-right: 10px;">
                        <label>Provisional</label>
                      </div>
                    </div>

                    <div class="dig-input-contain">
                      <!-- Dropdown for Select Diagnosis -->
                      <select *ngIf="diagnosisMode === 'select'"
                              class="diagnosis-input"
                              (change)="onCategorySelect($event)"
                              [value]="selectedDiagnosis">
                        <option value="">Select Diagnosis Category...</option>
                        <option *ngFor="let category of diagnosisCategories()"
                                [value]="category.Category">
                          {{ category.Category }}
                        </option>
                      </select>

                      <!-- Dropdown for Select Chapter -->
                      <select *ngIf="diagnosisMode === 'chapter'"
                              class="diagnosis-input"
                              (change)="onChapterSelect($event)"
                              [value]="selectedDiagnosis">
                        <option value="">Select Diagnosis Chapter...</option>
                        <option *ngFor="let chapter of diagnosisChapters()"
                                [value]="chapter.chapter">
                          {{ chapter.chapter }}
                        </option>
                      </select>

                      <!-- Text input for Manual Entry -->
                      <input *ngIf="diagnosisMode === 'manual'"
                             class="diagnosis-input"
                             type="text"
                             [(ngModel)]="selectedDiagnosis"
                             placeholder="Enter Diagnosis Manually">

                      <span (click)="addDiagnosis()"><img src="assets/icon/plus.png" alt=""> Add</span>
                    </div>
                  </div>
                </div>
              </div>



              <table class="new-complaints-table">
                <thead>
                  <tr>
                    <th>ICD Code</th>
                    <th>Diagnosis</th>
                    <th>Provisional</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let diagnosis of diagnoses">
                    <td>{{ diagnosis.code }}</td>
                    <td>{{ diagnosis.name }}</td>
                    <td>{{ diagnosis.provisional ? 'Yes' : 'No' }}</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit" (click)="editDiagnosis(diagnosis)">
                          <img src="assets/icon/edit.png" alt="">
                        </span>
                        <span class="icon" title="Delete" (click)="deleteDiagnosis(diagnosis)">
                          <img src="assets/icon/delete.png" alt="">
                        </span>
                      </span>
                    </td>
                  </tr>
                  <tr *ngIf="diagnoses.length === 0">
                    <td colspan="4" style="text-align: center; color: #666; padding: 20px;">
                      No diagnoses added yet. Use the form above to add diagnoses.
                    </td>
                  </tr>
                </tbody>
              </table>
