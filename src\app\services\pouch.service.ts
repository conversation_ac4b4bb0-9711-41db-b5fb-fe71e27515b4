import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';

PouchDB.plugin(PouchDBFind);

// Type for complaint documents (can extend if needed)
interface ComplaintDoc {
  _id: string;
  type: 'tblpatientcomplaints';
  ComplaintName?: string;
  [key: string]: any;
}

@Injectable({ providedIn: 'root' })
export class PouchService {
  private db: PouchDB.Database;

  constructor() {
    this.db = new PouchDB('remedinova');
  }

  // Query any docs by type; here used for 'tblpatientcomplaints'
  async findByType(type: string): Promise<ComplaintDoc[]> {
    // ensure index on type exists
    await this.db.createIndex({ index: { fields: ['type'] } });

    const result = await this.db.find({
      selector: { type },
      fields: ['_id', 'type', 'ComplaintName'],
      limit: 10000,
      sort: ['_id']
    });

    // Narrow the type safely
    const docs = result.docs as ComplaintDoc[];
    // Log the full array and each ComplaintName if present
    console.log(`${type} array:`, docs);
    docs.forEach(d => {
      if (d.ComplaintName !== undefined) {
        console.log('ComplaintName:', d.ComplaintName);
      }
    });
    return docs;
  }
}
