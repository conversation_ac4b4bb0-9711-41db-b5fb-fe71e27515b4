
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Component, OnInit, ChangeDetectorRef, NgZone } from '@angular/core';
import { PatientHistoryPage } from '../patient-history/patient-history.page';

@Component({
  selector: 'app-tabs',
  templateUrl: './tabs.page.html',
  styleUrls: ['./tabs.page.scss'],
  standalone: true,
  imports: [IonicModule,  CommonModule, FormsModule, PatientHistoryPage]
})
export class TabsPage implements OnInit {


 selectedTab: string = 'patientHistory';

// tabs = [
//   { label: 'Patient History', value: 'patientHistory' },
//   { label: 'Complaints', value: 'complaints' },
//   { label: 'Past Records', value: 'pastRecords' },
//   { label: 'Parameters', value: 'parameters' },
//   { label: 'Diagnosis', value: 'diagnosis' },
//   { label: 'Medicines', value: 'medicines' },
//   { label: 'Intervention', value: 'intervention' },
//   { label: 'Investigations', value: 'investigations' },
//   { label: 'Counseling', value: 'counseling' },
//   { label: 'Referrals', value: 'referrals' }
// ];

constructor(
  private ngZone: NgZone,
  private cdRef: ChangeDetectorRef
) {}

  ngOnInit() {
    // Optional: Initialization logic here
  }
switchTab(tab: string) {
  this.ngZone.run(() => {
    this.selectedTab = tab;
    this.cdRef.detectChanges();

    // Update active class on buttons
    setTimeout(() => {
      const buttons = document.querySelectorAll('.button-native');
      buttons.forEach(btn => btn.classList.remove('active'));

      const activeButton = document.querySelector(`[onclick*="${tab}"]`) ||
                          Array.from(buttons).find(btn =>
                            btn.textContent?.toLowerCase().replace(/\s+/g, '') ===
                            tab.toLowerCase().replace(/([A-Z])/g, ' $1').trim().replace(/\s+/g, '')
                          );

      if (activeButton) {
        activeButton.classList.add('active');
      }
    }, 10);
  });
}




  // --------------------------

  pastRecords = [
  { date: '05th May,2025', time: '09:15 AM', details: 'General Xray (1 Image)', attendedBy: 'Nr. Shilpa Sharma' },
  { date: '05th May,2025', time: '09:30 AM', details: 'Dermatoscope (4 Images)', attendedBy: 'Nr. Shilpa Sharma' },
  { date: '05th May,2025', time: '09:45 AM', details: 'ECG (1 Image)', attendedBy: 'Nr. Shilpa Sharma' },
  { date: '05th May,2025', time: '10:00 AM', details: 'Report No. 124', attendedBy: 'Nr. Shilpa Sharma' },
  { date: '05th May,2025', time: '10:30 AM', details: 'Report No. 122', attendedBy: 'Nr. Shilpa Sharma' }
];

pageSizes = [5, 10, 25];
pageSize = 5;
currentPage = 1;
totalItems = 50;

get currentPageRange() {
  const start = (this.currentPage - 1) * this.pageSize + 1;
  const end = Math.min(this.currentPage * this.pageSize, this.totalItems);
  return `${start.toString().padStart(2, '0')}-${end.toString().padStart(2, '0')}`;
}

get totalPages() {
  return Math.ceil(this.totalItems / this.pageSize);
}

previousPage() {
  if (this.currentPage > 1) this.currentPage--;
}

nextPage() {
  if (this.currentPage < this.totalPages) this.currentPage++;
}

deleteRecord(record: any) {
  console.log('Delete record:', record);
}

removeFilter(type: string) {
  console.log(`Removing filter of type: ${type}`);
  // Add your actual logic to remove filter here
}


// complaints-section.component.ts
otherComplaints = [
    { snomed: '271835004', icd: '-', text: 'Abdominal Swelling', since: '2 Hours' },
    { snomed: '125605004', icd: '-', text: 'Pain In The Fractured Part', since: '2 Days' },
    { snomed: '386861006, 82272006.', icd: '-', text: 'Fever And Cold', since: '5 Hours' },
  ];

  //test-card.component.ts
  // These @Input() properties should be in the child component, not here.

  // ------------------- parameters.page.ts
    physiologyTests = [
    { name: 'Temperature', value: '98.6°F', icon: 'thermometer', status: 'connected' },
    { name: 'Temperature (ASHA+)', value: '-', icon: 'thermometer', status: 'not-connected' },
    { name: 'Stethoscope', value: 'Normal', icon: 'stethoscope', status: 'connected' },
    { name: 'Stethoscope (ASHA+)', value: '-', icon: 'stethoscope', status: 'not-connected' },
    { name: 'Spirometer', value: '-', icon: 'pulse', status: 'not-connected' },
    { name: 'Blood Pressure', value: '120/80 mmHg', icon: 'water', status: 'connected' },
    { name: 'SpO2', value: '98%', icon: 'heart', status: 'connected' },
    { name: 'ECG', value: '-', icon: 'analytics', status: 'not-connected' },
    { name: 'ECG Interpretation', value: '-', icon: 'document-text', status: 'disabled' },
    { name: 'Fetal Doppler (FD)', value: '-', icon: 'body', status: 'not-connected' },
    { name: 'Fetal Doppler (Fetoscope)', value: '-', icon: 'body', status: 'not-connected' },
    { name: 'Auto Refractometer', value: '-', icon: 'eye', status: 'not-connected' },
    { name: 'X-Ray', value: '-', icon: 'image', status: 'not-connected' }
  ];

  bloodTests = [
    { name: 'Glucose', value: '-', icon: 'water', status: 'not-connected' },
    { name: 'Glucose (Wireless)', value: '-', icon: 'water', status: 'not-connected' },
    { name: 'Hemoglobin', value: '-', icon: 'water', status: 'not-connected' },
    { name: 'Lipid Profile', value: '-', icon: 'water', status: 'not-connected' },
    { name: 'Optical Reader', value: '-', icon: 'camera', status: 'not-connected' },
    { name: 'Urine Test', value: '-', icon: 'flask', status: 'not-connected' },
    { name: 'HbA1c (Wireless)', value: '-', icon: 'pulse', status: 'not-connected' },
    { name: 'WBC Differential', value: '-', icon: 'git-branch', status: 'not-connected' }
  ];

  // ------------------ Diagnosis Tab ------------------

diagnosisMode: 'select' | 'chapter' | 'manual' = 'select';

selectedDiagnosis: string = '';
isProvisional: boolean = false;

diagnoses: { code: string; name: string; provisional: boolean }[] = [
  { code: 'A42.1', name: 'Abdominal Actinomycosis', provisional: true },
  { code: 'A19.0', name: 'Acute Miliary Tuberculosis Of A Single Specified Site', provisional: true },
  { code: 'J00', name: 'Acute Nasopharyngitis [Common Cold]', provisional: false },
  { code: 'Y45.5', name: '4-Aminophenol Derivatives', provisional: false }
];

addDiagnosis() {
  if (!this.selectedDiagnosis.trim()) {
    return;
  }

  const newDiagnosis = {
    code: this.generateICDCode(), // You could enhance this
    name: this.selectedDiagnosis,
    provisional: this.isProvisional
  };

  this.diagnoses.push(newDiagnosis);
  this.selectedDiagnosis = '';
  this.isProvisional = false;
}

editDiagnosis(item: any) {
  this.selectedDiagnosis = item.name;
  this.isProvisional = item.provisional;
  this.deleteDiagnosis(item); // Temporarily remove to re-add
}

deleteDiagnosis(item: any) {
  this.diagnoses = this.diagnoses.filter(d => d !== item);
}

generateICDCode(): string {
  // You can replace this with a real ICD lookup service
  const code = 'X' + Math.floor(100 + Math.random() * 900); // Mock code
  return code;
}

// ------------------- medicien tab
newMedicine = {
  drugForm: '',
  name: '',
  instructions: '',
  dosage: '',
  frequency: '',
  days: null,
};

diagnosisList = [
  { code: 'A42.1', name: 'Abdominal Actinomycosis', provisional: true },
  { code: 'A19.0', name: 'Acute Military Tuberculosis Of A Single Specified Site', provisional: true },
  { code: 'J00', name: 'Acute Nasopharyngitis [Common Cold]', provisional: false },
  { code: 'Y45.5', name: '4-Aminophenol Derivatives', provisional: false }
];

addMedicine() {
  console.log('Adding medicine:', this.newMedicine);
  // push to list / save to backend as needed
}

fetchPreviousPrescription() {
  console.log('Fetching previous prescriptions...');
}

// <!-- --------------- intervention tab -->
intervention = {
  treatmentPlan: '',
  clinicalObservations: '',
  followUpDate: null
};
}
