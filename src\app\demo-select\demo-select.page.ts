import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonHeader, IonToolbar, IonTitle, IonContent, IonItem,
  IonLabel, IonSelect, IonSelectOption, IonSpinner
} from '@ionic/angular/standalone';
import { PouchService } from '../services/pouch.service';

@Component({
  standalone: true,
  selector: 'app-demo-select',
  imports: [
    CommonModule,
    IonHeader, IonToolbar, IonTitle, IonContent,
    IonItem, IonLabel, IonSelect, IonSelectOption, IonSpinner
  ],
  template: `
<ion-header>
  <ion-toolbar>
    <ion-title>Complaint Selector</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <div *ngIf="loading(); else contentTpl" style="display: flex; align-items: center; gap: 8px;">
    <ion-spinner name="crescent"></ion-spinner>
    <span>Loading complaints...</span>
  </div>

  <ng-template #contentTpl>
    <ion-item>
      <ion-label>Complaint</ion-label>
      <ion-select interface="popover" [value]="selected()" (ionChange)="onSelect($event.detail.value)">
        <ion-select-option *ngFor="let c of complaints()" [value]="c.ComplaintName">
          {{ c.ComplaintName }}
        </ion-select-option>
      </ion-select>
    </ion-item>

    <div *ngIf="selected()" style="margin-top: 20px; font-weight: bold;">
      You selected: <span>{{ selected() }}</span>
    </div>
  </ng-template>
</ion-content>
  `
})
export class DemoSelectPage implements OnInit {
  loading = signal(true);
  complaints = signal<{ _id: string; ComplaintName: string }[]>([]);
  selected = signal<string | null>(null);

  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    try {
      const rows = await this.pouch.findByType('tblpatientcomplaints');
      this.complaints.set(rows);
    } catch (err) {
      console.error('Error fetching complaints', err);
    } finally {
      this.loading.set(false);
    }
  }

  onSelect(name: string) {
    this.selected.set(name);
  }
}
