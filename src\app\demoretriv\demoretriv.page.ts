import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PouchService } from '../services/pouch.service';

@Component({
  standalone: true,
  selector: 'app-demoretriv',
  imports: [
    CommonModule
  ],
  template: `
<div style="padding: 20px;">


  <div *ngIf="loading()">
    Loading complaints...
  </div>

  <div *ngIf="!loading()">
    <button (click)="printComplaintsToConsole()" style="padding: 10px 15px; margin-bottom: 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
      Print
    </button>

    <!-- Simple HTML Dropdown -->
    <div style="margin-bottom: 20px;">
      <label style="display: block; margin-bottom: 10px; font-weight: bold;">
        Select :
      </label>
      <select
        (change)="onSelectHtml($event)"
        >
        <option value="">Choose a complaint...</option>
        <option
          *ngFor="let complaint of complaints()"
          [value]="complaint.ComplaintName">
          {{ complaint.ComplaintName }}
        </option>
      </select>
    </div>

    <!-- Selected complaint display in div -->
    <div *ngIf="selected()" >
      <h3 style="margin: 0 0 10px 0;">Complaint Name:</h3>
      <span style=" color: #2e7d32;">{{ selected() }}</span>
    </div>
  </div>
</div>
  `,
})
export class DemoretrivPage implements OnInit {
  loading = signal(true);
  complaints = signal<{ _id: string; ComplaintName: string }[]>([]);
  selected = signal<string | null>(null);

  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    try {
      console.log('=== LOADING tblpatientcomplaints ===');

      // Ensure data is seeded from assets
      await this.pouch.ensureSeededFromAssets();

      const list = await this.pouch.findByType('tblpatientcomplaints');
      console.log('Raw tblpatientcomplaints data:', list);

      // Filter and properly type the complaints
      const filtered = list
        .filter((c: any) => c.ComplaintName && typeof c.ComplaintName === 'string')
        .map((c: any) => ({
          _id: c._id,
          ComplaintName: c.ComplaintName as string
        }));

      console.log('Filtered complaints with ComplaintName:', filtered);

      this.complaints.set(filtered);
      console.log('Total complaints loaded:', filtered.length);

      // Print each ComplaintName
      filtered.forEach((complaint, index) => {
        console.log(`ComplaintName ${index + 1}:`, complaint.ComplaintName);
      });

    } catch (e) {
      console.error('Error fetching complaints', e);
    } finally {
      this.loading.set(false);
    }
  }

  onSelectHtml(event: any) {
    const value = event.target.value;
    this.selected.set(value);
    console.log('Selected ComplaintName:', value);
  }

  printComplaintsToConsole() {
    console.log('=== PRINTING tblpatientcomplaints ARRAY ===');
    console.log('Full tblpatientcomplaints array:', this.complaints());

    console.log('=== ComplaintName VALUES ===');
    this.complaints().forEach((complaint, index) => {
      console.log(`${index + 1}. ComplaintName: "${complaint.ComplaintName}"`);
    });

    console.log('=== SAMPLE DATA STRUCTURE ===');
    if (this.complaints().length > 0) {
      console.log('Sample complaint object:', this.complaints()[0]);
    }
    console.log('=== END ===');
  }
}
