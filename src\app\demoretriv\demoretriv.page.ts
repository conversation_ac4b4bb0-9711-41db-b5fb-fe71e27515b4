import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonHeader, IonToolbar, IonTitle, IonContent,
  IonItem, IonLabel, IonSelect, IonSelectOption, IonSpinner
} from '@ionic/angular/standalone';
import { PouchService } from '../';

@Component({
  standalone: true,
  selector: 'app-demo-select',
  imports: [
    CommonModule,
    IonHeader, IonToolbar, IonTitle, IonContent,
    IonItem, IonLabel, IonSelect, IonSelectOption, IonSpinner
  ],
  template: `
<ion-header>
  <ion-toolbar>
    <ion-title>Complaint Selector</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <div *ngIf="loading(); else content">
    <ion-spinner name="crescent"></ion-spinner> Loading...
  </div>

  <ng-template #content>
    <ion-item>
      <ion-label>Complaint</ion-label>
      <ion-select [value]="selected()" (ionChange)="onSelect($event.detail.value)">
        <ion-select-option *ngFor="let c of complaints()" [value]="c.ComplaintName">
          {{ c.ComplaintName }}
        </ion-select-option>
      </ion-select>
    </ion-item>

    <div *ngIf="selected()" style="margin-top:16px;">
      Selected Complaint: <strong>{{ selected() }}</strong>
    </div>
  </ng-template>
</ion-content>
  `,
})
export class DemoretrivPage implements OnInit {
   loading = signal(true);
  complaints = signal<{ _id: string; ComplaintName?: string }[]>([]);
  selected = signal<string | null>(null);

  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    try {
      const list = await this.pouch.findByType('tblpatientcomplaints');
      // Filter out entries without a name (optional)
      const filtered = list.filter(c => c.ComplaintName);
      this.complaints.set(filtered as { _id: string; ComplaintName: string }[]);
      if (filtered.length) {
        this.selected.set(filtered[0].ComplaintName!);
      }
    } catch (e) {
      console.error('Error fetching complaints', e);
    } finally {
      this.loading.set(false);
    }
  }

  onSelect(name: string) {
    this.selected.set(name);
  }
}
