import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonHeader, IonToolbar, IonTitle, IonContent, IonItem,
  IonLabel, IonSelect, IonSelectOption, IonSpinner
} from '@ionic/angular/standalone';
import { PouchService } from '../services/pouch.service';

@Component({
  standalone: true,
  selector: 'app-demoretriv',
  imports: [
    CommonModule,
    IonHeader, IonToolbar, IonTitle, IonContent,
    IonItem, IonLabel, IonSelect, IonSelectOption, IonSpinner
  ],
  template: `
<ion-header>
  <ion-toolbar>
    <ion-title>Demo Retrieval - Patient Complaints</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <div *ngIf="loading(); else contentTpl" style="display: flex; align-items: center; gap: 8px;">
    <ion-spinner name="crescent"></ion-spinner>
    <span>Loading complaints...</span>
  </div>

  <ng-template #contentTpl>
    <ion-item>
      <ion-label>Select Patient Complaint</ion-label>
      <ion-select 
        interface="popover" 
        [value]="selectedComplaint()" 
        (ionChange)="onComplaintSelect($event.detail.value)"
        placeholder="Choose a complaint">
        <ion-select-option 
          *ngFor="let complaint of complaints()" 
          [value]="complaint.ComplaintName">
          {{ complaint.ComplaintName }}
        </ion-select-option>
      </ion-select>
    </ion-item>

    <div *ngIf="selectedComplaint()" style="margin-top: 20px; padding: 16px; background-color: #f5f5f5; border-radius: 8px;">
      <h3 style="margin: 0 0 8px 0; color: #333;">Selected Complaint:</h3>
      <span style="font-size: 16px; font-weight: 500; color: #007bff;">{{ selectedComplaint() }}</span>
    </div>

    <div *ngIf="complaints().length === 0 && !loading()" style="text-align: center; margin-top: 40px; color: #666;">
      <p>No complaints found in the database.</p>
      <p style="font-size: 14px;">Make sure the data is properly seeded.</p>
    </div>
  </ng-template>
</ion-content>
  `
})
export class DemoretrivPage implements OnInit {
  loading = signal(true);
  complaints = signal<{ _id: string; ComplaintName: string; ReMeDi_Code?: string }[]>([]);
  selectedComplaint = signal<string | null>(null);

  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    try {
      // Ensure data is seeded from assets
      await this.pouch.ensureSeededFromAssets();
      
      // Fetch complaints from tblpatientcomplaints
      const rows = await this.pouch.findByType('tblpatientcomplaints');
      console.log('Fetched complaints:', rows);
      
      this.complaints.set(rows);
    } catch (err) {
      console.error('Error fetching complaints:', err);
    } finally {
      this.loading.set(false);
    }
  }

  onComplaintSelect(complaintName: string) {
    this.selectedComplaint.set(complaintName);
    console.log('Selected complaint:', complaintName);
  }
}
