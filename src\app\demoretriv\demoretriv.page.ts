import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PouchService } from '../services/pouch.service';

@Component({
  standalone: true,
  selector: 'app-demoretriv',
  imports: [
    CommonModule
  ],
  template: `
<div style="padding: 20px;">
  <h2>Patient Complaints</h2>

  <div *ngIf="loading()">
    Loading complaints...
  </div>

  <div *ngIf="!loading()">
    <button (click)="printComplaintsToConsole()" style="padding: 10px 15px; margin-bottom: 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
      Print tblpatientcomplaints to Console
    </button>

    <!-- Simple HTML Dropdown -->
    <div style="margin-bottom: 20px;">
      <label style="display: block; margin-bottom: 10px; font-weight: bold;">
        Select Patient Complaint:
      </label>
      <select
        (change)="onSelectHtml($event)"
        style="width: 100%; padding: 10px; font-size: 16px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">Choose a complaint...</option>
        <option
          *ngFor="let complaint of complaints()"
          [value]="complaint.ComplaintName">
          {{ complaint.ComplaintName }}
        </option>
      </select>
    </div>

    <!-- Selected complaint display in div -->
    <div *ngIf="selected()" style="margin-top: 20px; padding: 15px; background-color: #e8f5e8; border-radius: 5px; border: 1px solid #4caf50;">
      <h3 style="margin: 0 0 10px 0;">Selected ComplaintName:</h3>
      <span style="font-size: 18px; font-weight: bold; color: #2e7d32;">{{ selected() }}</span>
    </div>
  </div>
</div>
  `,
})
export class DemoretrivPage implements OnInit {
   loading = signal(true);
  complaints = signal<{ _id: string; ComplaintName?: string }[]>([]);
  selected = signal<string | null>(null);

  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    try {
      console.log('=== LOADING tblpatientcomplaints ===');
      const list = await this.pouch.findByType('tblpatientcomplaints');
      console.log('Raw tblpatientcomplaints data:', list);

      // Filter out entries without a name
      const filtered = list.filter(c => c.ComplaintName);
      console.log('Filtered complaints with ComplaintName:', filtered);

      this.complaints.set(filtered as { _id: string; ComplaintName: string }[]);
      console.log('Total complaints loaded:', filtered.length);

      // Print each ComplaintName
      filtered.forEach((complaint, index) => {
        console.log(`ComplaintName ${index + 1}:`, complaint.ComplaintName);
      });

    } catch (e) {
      console.error('Error fetching complaints', e);
    } finally {
      this.loading.set(false);
    }
  }

  onSelectHtml(event: any) {
    const value = event.target.value;
    this.selected.set(value);
    console.log('Selected ComplaintName:', value);
  }

  printComplaintsToConsole() {
    console.log('=== PRINTING tblpatientcomplaints ARRAY ===');
    console.log('Full tblpatientcomplaints array:', this.complaints());

    console.log('=== ComplaintName VALUES ===');
    this.complaints().forEach((complaint, index) => {
      console.log(`${index + 1}. ComplaintName: "${complaint.ComplaintName}"`);
    });

    console.log('=== SAMPLE DATA STRUCTURE ===');
    if (this.complaints().length > 0) {
      console.log('Sample complaint object:', this.complaints()[0]);
    }
    console.log('=== END ===');
  }
}
