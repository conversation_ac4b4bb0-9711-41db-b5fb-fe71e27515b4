import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonHeader, IonToolbar, IonTitle, IonContent, IonItem,
  IonLabel, IonSelect, IonSelectOption, IonSpinner
} from '@ionic/angular/standalone';
import { PouchService } from '../services/pouch.service';

@Component({
  standalone: true,
  selector: 'app-demoretriv',
  imports: [
    CommonModule,
    IonHeader, IonToolbar, IonTitle, IonContent,
    IonItem, IonLabel, IonSelect, IonSelectOption, IonSpinner
  ],
  template: `
<ion-header>
  <ion-toolbar>
    <ion-title>Demo Retrieval - Patient Complaints</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <div *ngIf="loading(); else contentTpl" style="display: flex; align-items: center; gap: 8px;">
    <ion-spinner name="crescent"></ion-spinner>
    <span>Loading complaints...</span>
  </div>

  <ng-template #contentTpl>
    <!-- Debug Information -->
    <div style="background: #f0f0f0; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
      <h4>Debug Info:</h4>
      <p>Loading: {{ loading() }}</p>
      <p>Complaints count: {{ complaints().length }}</p>
      <p>Has test data: {{ testComplaints().length }}</p>
    </div>

    <!-- Dropdown with database data -->
    <ion-item>
      <ion-label>Select Patient Complaint (Database)</ion-label>
      <ion-select
        interface="popover"
        [value]="selectedComplaint()"
        (ionChange)="onComplaintSelect($event.detail.value)"
        placeholder="Choose a complaint">
        <ion-select-option
          *ngFor="let complaint of complaints()"
          [value]="complaint.ComplaintName">
          {{ complaint.ComplaintName }}
        </ion-select-option>
      </ion-select>
    </ion-item>

    <!-- Test dropdown with hardcoded data -->
    <ion-item>
      <ion-label>Select Test Complaint (Hardcoded)</ion-label>
      <ion-select
        interface="popover"
        [value]="selectedTestComplaint()"
        (ionChange)="onTestComplaintSelect($event.detail.value)"
        placeholder="Choose a test complaint">
        <ion-select-option
          *ngFor="let complaint of testComplaints()"
          [value]="complaint.name">
          {{ complaint.name }}
        </ion-select-option>
      </ion-select>
    </ion-item>

    <div *ngIf="selectedComplaint()" style="margin-top: 20px; padding: 16px; background-color: #e3f2fd; border-radius: 8px;">
      <h3 style="margin: 0 0 8px 0; color: #333;">Selected Database Complaint:</h3>
      <span style="font-size: 16px; font-weight: 500; color: #007bff;">{{ selectedComplaint() }}</span>
    </div>

    <div *ngIf="selectedTestComplaint()" style="margin-top: 20px; padding: 16px; background-color: #f3e5f5; border-radius: 8px;">
      <h3 style="margin: 0 0 8px 0; color: #333;">Selected Test Complaint:</h3>
      <span style="font-size: 16px; font-weight: 500; color: #7b1fa2;">{{ selectedTestComplaint() }}</span>
    </div>

    <div *ngIf="complaints().length === 0 && !loading()" style="text-align: center; margin-top: 40px; color: #666;">
      <p>No complaints found in the database.</p>
      <p style="font-size: 14px;">Make sure the data is properly seeded.</p>
    </div>
  </ng-template>
</ion-content>
  `
})
export class DemoretrivPage implements OnInit {
  loading = signal(true);
  complaints = signal<{ _id: string; ComplaintName: string; ReMeDi_Code?: string }[]>([]);
  selectedComplaint = signal<string | null>(null);

  // Test data for debugging
  testComplaints = signal([
    { name: 'Headache', code: 'T001' },
    { name: 'Fever', code: 'T002' },
    { name: 'Cough', code: 'T003' },
    { name: 'Stomach Pain', code: 'T004' },
    { name: 'Back Pain', code: 'T005' }
  ]);
  selectedTestComplaint = signal<string | null>(null);

  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    console.log('DemoretrivPage: ngOnInit started');
    try {
      console.log('DemoretrivPage: Ensuring data is seeded...');
      // Ensure data is seeded from assets
      await this.pouch.ensureSeededFromAssets();

      console.log('DemoretrivPage: Fetching complaints from database...');
      // Fetch complaints from tblpatientcomplaints
      const rows = await this.pouch.findByType('tblpatientcomplaints');
      console.log('DemoretrivPage: Fetched complaints:', rows);
      console.log('DemoretrivPage: Number of complaints:', rows.length);

      if (rows.length > 0) {
        console.log('DemoretrivPage: First complaint:', rows[0]);
      }

      this.complaints.set(rows);
      console.log('DemoretrivPage: Complaints signal updated');
    } catch (err) {
      console.error('DemoretrivPage: Error fetching complaints:', err);
    } finally {
      this.loading.set(false);
      console.log('DemoretrivPage: Loading finished');
    }
  }

  onComplaintSelect(complaintName: string) {
    this.selectedComplaint.set(complaintName);
    console.log('Selected complaint:', complaintName);
  }

  onTestComplaintSelect(complaintName: string) {
    this.selectedTestComplaint.set(complaintName);
    console.log('Selected test complaint:', complaintName);
  }
}
