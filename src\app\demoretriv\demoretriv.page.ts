import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PouchService } from '../services/pouch.service';

@Component({
  standalone: true,
  selector: 'app-demoretriv',
  imports: [
    CommonModule
  ],
  template: `
<div style="padding: 20px;">
  <h2>Diagnosis Category Retrieval</h2>

  <!-- Debug Info -->
  <div style="background: #f0f0f0; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
    <p><strong>Loading:</strong> {{ loading() }}</p>
    <p><strong>Categories Count:</strong> {{ categories().length }}</p>
    <p><strong>Database Status:</strong> {{ dbStatus() }}</p>
  </div>

  <div *ngIf="loading()">
    Loading diagnosis categories...
  </div>

  <div *ngIf="!loading()">
    <button (click)="printCategoriesToConsole()" style="padding: 10px 15px; margin-bottom: 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
      Print tbldiagnosiscategory to Console
    </button>

    <button (click)="reloadData()" style="padding: 10px 15px; margin-bottom: 20px; margin-left: 10px; background: #28a745; color: white; border: none; border-radius: 5px;">
      Reload Data
    </button>

    <!-- Simple HTML Dropdown -->
    <div style="margin-bottom: 20px;">
      <label style="display: block; margin-bottom: 10px; font-weight: bold;">
        Select Diagnosis Category ({{ categories().length }} available):
      </label>
      <select
        (change)="onSelectHtml($event)"
        style="width: 100%; padding: 8px; font-size: 16px; border: 1px solid #ccc; border-radius: 4px;">
        <option value="">Choose a category...</option>
        <option
          *ngFor="let category of categories(); let i = index"
          [value]="category.Category">
          {{ i + 1 }}. {{ category.Category }}
        </option>
      </select>
    </div>

    <!-- Selected category display in div -->
    <div *ngIf="selected()" style="margin-top: 20px; padding: 15px; background-color: #e8f5e8; border-radius: 5px; border: 1px solid #4caf50;">
      <h3 style="margin: 0 0 10px 0;">Selected Category:</h3>
      <span style="font-size: 18px; font-weight: bold; color: #2e7d32;">{{ selected() }}</span>
    </div>

    <!-- Show message if no data -->
    <div *ngIf="categories().length === 0" style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border-radius: 5px; border: 1px solid #ffeaa7;">
      <h3>No diagnosis categories found!</h3>
      <p>This could mean:</p>
      <ul>
        <li>Data is not seeded yet</li>
        <li>Database connection issue</li>
        <li>Data structure problem</li>
      </ul>
      <p>Check the browser console for more details.</p>
    </div>

    <!-- Table showing all records -->
    <div *ngIf="categories().length > 0" style="margin-top: 30px;">
      <h3>All Diagnosis Categories ({{ categories().length }} records):</h3>
      <div style="overflow-x: auto; max-height: 400px; border: 1px solid #ddd; border-radius: 5px;">
        <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
          <thead style="background-color: #f8f9fa; position: sticky; top: 0;">
            <tr>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">#</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Category</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Category_Id</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">IsDeleted</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">subchapterid</th>
              <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">domain</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let category of categories(); let i = index" [style.background-color]="i % 2 === 0 ? '#f9f9f9' : 'white'">
              <td style="border: 1px solid #ddd; padding: 8px;">{{ i + 1 }}</td>
              <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">{{ category.Category }}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">{{ category.Category_Id }}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">{{ category.IsDeleted }}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">{{ category.subchapterid || 'N/A' }}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">{{ category.domain || 'N/A' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
  `,
})
export class DemoretrivPage implements OnInit {
  loading = signal(true);
  categories = signal<{ _id: string; Category: string; Category_Id: string; IsDeleted: string; subchapterid?: string; domain?: string }[]>([]);
  selected = signal<string | null>(null);
  dbStatus = signal<string>('Initializing...');

  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    await this.loadData();
  }

  async loadData() {
    try {
      this.loading.set(true);
      this.dbStatus.set('Connecting to database...');
      console.log('=== DEMORETRIV: Loading tbldiagnosiscategory ===');

      // Check database info first
      const dbInfo = await this.pouch.getInfo();
      console.log('Database info:', dbInfo);
      this.dbStatus.set(`Connected - ${dbInfo.doc_count} documents`);

      // Ensure data is seeded from assets
      console.log('Ensuring data is seeded...');
      await this.pouch.ensureSeededFromAssets();

      // Get updated database info after seeding
      const dbInfoAfter = await this.pouch.getInfo();
      console.log('Database info after seeding:', dbInfoAfter);
      this.dbStatus.set(`Seeded - ${dbInfoAfter.doc_count} documents`);

      // Fetch diagnosis categories
      console.log('Fetching tbldiagnosiscategory...');
      const list = await this.pouch.findByType('tbldiagnosiscategory');
      console.log('Raw data from findByType:', list);
      console.log('Number of raw records:', list.length);

      if (list.length === 0) {
        console.warn('No records found for type "tbldiagnosiscategory"');
        this.dbStatus.set('No tbldiagnosiscategory found');
        return;
      }

      // Log first few records to see structure
      console.log('First 3 raw records:', list.slice(0, 3));

      // Filter and properly type the categories
      const filtered = list
        .filter((c: any) => {
          const hasCategory = c.Category && typeof c.Category === 'string';
          if (!hasCategory) {
            console.log('Filtering out record without Category:', c);
          }
          return hasCategory;
        })
        .map((c: any) => ({
          _id: c._id,
          Category: c.Category as string,
          Category_Id: c.Category_Id || '',
          IsDeleted: c.IsDeleted || '0',
          subchapterid: c.subchapterid,
          domain: c.domain
        }));

      console.log('Filtered categories:', filtered);
      console.log('Number of filtered categories:', filtered.length);

      this.categories.set(filtered);
      this.dbStatus.set(`Loaded ${filtered.length} categories`);

      // Print each Category
      console.log('=== ALL DIAGNOSIS CATEGORIES ===');
      filtered.forEach((category, index) => {
        console.log(`${index + 1}. Category: "${category.Category}" (ID: ${category.Category_Id})`);
      });
      console.log('=== END DIAGNOSIS CATEGORIES ===');

    } catch (e) {
      console.error('Error in loadData:', e);
      this.dbStatus.set('Error loading data');
    } finally {
      this.loading.set(false);
    }
  }

  async reloadData() {
    console.log('Reloading data...');
    await this.loadData();
  }

  onSelectHtml(event: any) {
    const value = event.target.value;
    this.selected.set(value);
    console.log('Selected Category:', value);
  }

  printCategoriesToConsole() {
    console.log('=== PRINTING tbldiagnosiscategory ARRAY ===');
    console.log('Full categories array:', this.categories());
    console.log('Array length:', this.categories().length);

    console.log('=== CATEGORY VALUES ===');
    this.categories().forEach((category, index) => {
      console.log(`${index + 1}. Category: "${category.Category}"`);
      console.log(`   Category_Id: "${category.Category_Id}"`);
      console.log(`   IsDeleted: "${category.IsDeleted}"`);
      console.log(`   subchapterid: "${category.subchapterid || 'N/A'}"`);
      console.log(`   domain: "${category.domain || 'N/A'}"`);
      console.log('   ---');
    });

    console.log('=== SAMPLE DATA STRUCTURE ===');
    if (this.categories().length > 0) {
      console.log('Sample category object:', this.categories()[0]);
    }
    console.log('=== END ===');
  }
}
