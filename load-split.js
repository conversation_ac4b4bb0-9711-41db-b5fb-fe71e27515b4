// load-split.js
const fs = require('fs');
const crypto = require('crypto');
const PouchDB = require('pouchdb');
PouchDB.plugin(require('pouchdb-find'));

/**
 * Convert your top-level JSON object into an array of PouchDB docs.
 * - Arrays -> many docs (with type = arrayName)
 * - Single objects -> one doc (with type = keyName)
 * - _id strategy:
 *     - If item has a numeric/string Id or <name>_Id field, use it.
 *     - Else, fall back to a stable hash of the item.
 */
function jsonToDocs(top) {
  const docs = [];

  for (const [key, value] of Object.entries(top)) {
    if (Array.isArray(value)) {
      value.forEach((item) => {
        const baseId =
          item.Id ?? item.id ??
          item[key + '_Id'] ??
          item[key + '_id'] ??
          item.Code ??
          item.code;

        const suffix = baseId != null ? String(baseId) : crypto.createHash('md5').update(JSON.stringify(item)).digest('hex');
        docs.push({
          _id: `${key}:${suffix}`,
          type: key,
          ...item,
        });
      });
    } else if (value && typeof value === 'object') {
      // single object -> single doc
      docs.push({
        _id: `${key}:1`,
        type: key,
        ...value,
      });
    } else {
      // primitives at top level (rare) -> keep in a small meta doc
      docs.push({
        _id: `meta:${key}`,
        type: 'meta',
        value,
      });
    }
  }

  return docs;
}

async function upsertBulk(db, docs, batchSize = 1000) {
  // Load existing revs to avoid conflicts when re-running
  const ids = docs.map(d => d._id);
  const existing = await db.allDocs({ keys: ids, include_docs: false });
  const revMap = new Map();
  existing.rows.forEach(row => {
    if (row.value && row.value.rev) revMap.set(row.key, row.value.rev);
  });

  // Attach _rev where present
  const toWrite = docs.map(d => revMap.has(d._id) ? { ...d, _rev: revMap.get(d._id) } : d);

  // Batch writes
  for (let i = 0; i < toWrite.length; i += batchSize) {
    const chunk = toWrite.slice(i, i + batchSize);
    const res = await db.bulkDocs(chunk);
    const errors = res.filter(r => r.error);
    if (errors.length) {
      console.error('Bulk errors in chunk', i / batchSize, errors.slice(0, 3));
      // You can add retry/backoff if needed
    }
  }
}

async function main() {
  const db = new PouchDB('remedinova'); // local LevelDB
  const raw = JSON.parse(fs.readFileSync('RemediNovaAPI.json', 'utf8'));

  // 1) Transform JSON -> array of docs
  const docs = jsonToDocs(raw);
  console.log(`Prepared ${docs.length} docs`);

  // 2) Upsert into PouchDB (safe to re-run)
  await upsertBulk(db, docs, 1000);

  // 3) Create helpful indexes for querying
  //    Example: search complaints by name and ReMeDi_Code
  await db.createIndex({ index: { fields: ['type', 'ComplaintName'] } });
  await db.createIndex({ index: { fields: ['type', 'ReMeDi_Code'] } });

  // (Add more indexes as needed, e.g., for lab tests: ['type','TestName'], etc.)

  // 4) Example queries
  // Find complaints with name starting with "Fever"
  const q1 = await db.find({
    selector: {
      type: 'tblpatientcomplaints',
      ComplaintName: { $regex: '^Fever' }
    },
    limit: 10
  });
  console.log('Complaints starting with "Fever":', q1.docs.map(d => d.ComplaintName));

  // Find a complaint by numeric/string code
  const q2 = await db.find({
    selector: {
      type: 'tblpatientcomplaints',
      ReMeDi_Code: '1009'   // example code in your data
    },
    limit: 5
  });
  console.log('Complaints with code 1009:', q2.docs.map(d => ({ id: d._id, name: d.ComplaintName })));
}

main().catch(console.error);
