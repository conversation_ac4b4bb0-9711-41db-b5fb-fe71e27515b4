import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';
import { PouchService } from '../../../services/pouch.service';

@Component({
  selector: 'app-diagnosis',
  templateUrl: './diagnosis.page.html',
  styleUrls: ['./diagnosis.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class DiagnosisPage implements OnInit {

  // ------------------ Diagnosis Tab ------------------
  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    try {
      console.log('=== LOADING DIAGNOSIS DATA ===');

      // Ensure data is seeded from assets
      await this.pouch.ensureSeededFromAssets();

      // Load diagnosis categories
      console.log('Loading tbldiagnosiscategory...');
      const categories = await this.pouch.findByType('tbldiagnosiscategory');
      console.log('Raw tbldiagnosiscategory data:', categories);
      console.log('Raw categories count:', categories.length);

      // Filter categories - check for Category field and IsDeleted
      const validCategories = categories
        .filter((c: any) => {
          console.log('Processing category:', c);
          return c.Category &&
                 typeof c.Category === 'string' &&
                 c.Category.trim() !== '' &&
                 (!c.IsDeleted || c.IsDeleted === '0');
        })
        .map((c: any) => ({
          _id: c._id,
          Category: c.Category.trim(),
          Category_Id: c.Category_Id,
          domain: c.domain
        }));

      console.log('Filtered categories:', validCategories);
      this.diagnosisCategories.set(validCategories);

      // Print each category
      validCategories.forEach((category, index) => {
        console.log(`Category ${index + 1}: "${category.Category}"`);
      });

      // Load diagnosis chapters
      console.log('Loading tbldiagnosischapter...');
      const chapters = await this.pouch.findByType('tbldiagnosischapter');
      console.log('Raw tbldiagnosischapter data:', chapters);
      console.log('Raw chapters count:', chapters.length);

      // Filter chapters - check for chapter field and isdeleted
      const validChapters = chapters
        .filter((c: any) => {
          console.log('Processing chapter:', c);
          return c.chapter &&
                 typeof c.chapter === 'string' &&
                 c.chapter.trim() !== '' &&
                 (!c.isdeleted || c.isdeleted === '0');
        })
        .map((c: any) => ({
          _id: c._id,
          chapter: c.chapter.replace(/\r/g, '').replace(/\n/g, '').trim(),
          id: c.id,
          domain: c.domain
        }));

      console.log('Filtered chapters:', validChapters);
      this.diagnosisChapters.set(validChapters);

      // Print each chapter
      validChapters.forEach((chapter, index) => {
        console.log(`Chapter ${index + 1}: "${chapter.chapter}"`);
      });

      console.log('=== FINAL RESULTS ===');
      console.log('Final loaded diagnosis categories:', validCategories.length);
      console.log('Final loaded diagnosis chapters:', validChapters.length);
      console.log('Categories signal value:', this.diagnosisCategories());
      console.log('Chapters signal value:', this.diagnosisChapters());

    } catch (error) {
      console.error('Error loading diagnosis data:', error);
    }
  }

  diagnosisMode: 'select' | 'chapter' | 'manual' = 'select';
  selectedDiagnosis: string = '';
  isProvisional: boolean = false;

  // Signals for dropdown data
  diagnosisCategories = signal<{ _id: string; Category: string; Category_Id?: string }[]>([]);
  diagnosisChapters = signal<{ _id: string; chapter: string; id?: string }[]>([]);

  diagnoses: { code: string; name: string; provisional: boolean }[] = [];

onDiagnosisModeChange(mode: 'select' | 'chapter' | 'manual') {
    this.diagnosisMode = mode;
    this.selectedDiagnosis = ''; // Clear selection when mode changes
    console.log('Diagnosis mode changed to:', mode);
  }

  onCategorySelect(event: any) {
    const categoryName = event.target.value;
    this.selectedDiagnosis = categoryName;
    console.log('Selected diagnosis category:', categoryName);
  }

  onChapterSelect(event: any) {
    const chapterName = event.target.value;
    this.selectedDiagnosis = chapterName;
    console.log('Selected diagnosis chapter:', chapterName);
  }

  addDiagnosis() {
    if (!this.selectedDiagnosis.trim()) {
      return;
    }

    const newDiagnosis = {
      code: this.generateICDCode(),
      name: this.selectedDiagnosis,
      provisional: this.isProvisional
    };

    this.diagnoses.push(newDiagnosis);
    this.selectedDiagnosis = '';
    this.isProvisional = false;
    console.log('Added diagnosis:', newDiagnosis);
  }

  editDiagnosis(item: any) {
    this.selectedDiagnosis = item.name;
    this.isProvisional = item.provisional;
    this.deleteDiagnosis(item); // Temporarily remove to re-add
  }

  deleteDiagnosis(item: any) {
    this.diagnoses = this.diagnoses.filter(d => d !== item);
  }

  generateICDCode(): string {
    // You can replace this with a real ICD lookup service
    const code = 'X' + Math.floor(100 + Math.random() * 900); // Mock code
    return code;
  }

  // Debug method to print all diagnosis data to console
  printDiagnosisDataToConsole() {
    console.log('=== PRINTING DIAGNOSIS DATA TO CONSOLE ===');
    console.log('Categories count:', this.diagnosisCategories().length);
    console.log('Chapters count:', this.diagnosisChapters().length);

    console.log('=== DIAGNOSIS CATEGORIES ===');
    this.diagnosisCategories().forEach((category, index) => {
      console.log(`${index + 1}. Category: "${category.Category}"`);
    });

    console.log('=== DIAGNOSIS CHAPTERS ===');
    this.diagnosisChapters().forEach((chapter, index) => {
      console.log(`${index + 1}. Chapter: "${chapter.chapter}"`);
    });

    console.log('=== CURRENT DIAGNOSES IN TABLE ===');
    this.diagnoses.forEach((diagnosis, index) => {
      console.log(`${index + 1}. ${diagnosis.code}: ${diagnosis.name} (Provisional: ${diagnosis.provisional})`);
    });
    console.log('=== END ===');
  }

  // Manual reload method for testing
  async reloadDiagnosisData() {
    console.log('=== MANUAL RELOAD TRIGGERED ===');
    await this.ngOnInit();
  }


}
