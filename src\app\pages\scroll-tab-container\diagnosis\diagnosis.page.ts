import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';
import { PouchService } from '../../../services/pouch.service';

@Component({
  selector: 'app-diagnosis',
  templateUrl: './diagnosis.page.html',
  styleUrls: ['./diagnosis.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class DiagnosisPage implements OnInit {

  // ------------------ Diagnosis Tab ------------------
  constructor(private pouch: PouchService) {}

  async ngOnInit() {
    try {
      // Ensure data is seeded from assets
      await this.pouch.ensureSeededFromAssets();

      // Load diagnosis categories
      const categories = await this.pouch.findByType('tbldiagnosiscategory');
      const validCategories = categories
        .filter((c: any) => c.Category && typeof c.Category === 'string')
        .map((c: any) => ({
          _id: c._id,
          Category: c.Category as string,
          Category_Id: c.Category_Id
        }));
      this.diagnosisCategories.set(validCategories);

      // Load diagnosis chapters
      const chapters = await this.pouch.findByType('tbldiagnosischapter');
      const validChapters = chapters
        .filter((c: any) => c.chapter && typeof c.chapter === 'string')
        .map((c: any) => ({
          _id: c._id,
          chapter: c.chapter.replace('\r', '').trim() as string,
          id: c.id
        }));
      this.diagnosisChapters.set(validChapters);

      console.log('Loaded diagnosis categories:', validCategories.length);
      console.log('Loaded diagnosis chapters:', validChapters.length);

    } catch (error) {
      console.error('Error loading diagnosis data:', error);
    }
  }

  diagnosisMode: 'select' | 'chapter' | 'manual' = 'select';
  selectedDiagnosis: string = '';
  isProvisional: boolean = false;

  // Signals for dropdown data
  diagnosisCategories = signal<{ _id: string; Category: string; Category_Id?: string }[]>([]);
  diagnosisChapters = signal<{ _id: string; chapter: string; id?: string }[]>([]);

  diagnoses: { code: string; name: string; provisional: boolean }[] = [];

onDiagnosisModeChange(mode: 'select' | 'chapter' | 'manual') {
    this.diagnosisMode = mode;
    this.selectedDiagnosis = ''; // Clear selection when mode changes
    console.log('Diagnosis mode changed to:', mode);
  }

  onCategorySelect(event: any) {
    const categoryName = event.target.value;
    this.selectedDiagnosis = categoryName;
    console.log('Selected diagnosis category:', categoryName);
  }

  onChapterSelect(event: any) {
    const chapterName = event.target.value;
    this.selectedDiagnosis = chapterName;
    console.log('Selected diagnosis chapter:', chapterName);
  }

  addDiagnosis() {
    if (!this.selectedDiagnosis.trim()) {
      return;
    }

    const newDiagnosis = {
      code: this.generateICDCode(),
      name: this.selectedDiagnosis,
      provisional: this.isProvisional
    };

    this.diagnoses.push(newDiagnosis);
    this.selectedDiagnosis = '';
    this.isProvisional = false;
    console.log('Added diagnosis:', newDiagnosis);
  }

  editDiagnosis(item: any) {
    this.selectedDiagnosis = item.name;
    this.isProvisional = item.provisional;
    this.deleteDiagnosis(item); // Temporarily remove to re-add
  }

  deleteDiagnosis(item: any) {
    this.diagnoses = this.diagnoses.filter(d => d !== item);
  }

  generateICDCode(): string {
    // You can replace this with a real ICD lookup service
    const code = 'X' + Math.floor(100 + Math.random() * 900); // Mock code
    return code;
  }


}
